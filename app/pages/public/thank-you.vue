<script setup lang="ts">
const { t } = useI18n();
const router = useRouter();
const route = useRoute();

// Get query parameters
const paymentStatus = route.query.payment as string;
const orderId = route.query.id as string;

// Reactive countdown
const countdown = ref(5);
const isRedirecting = ref(false);

// Auto-redirect logic
onMounted(() => {
  const interval = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(interval);
      isRedirecting.value = true;
      // G<PERSON><PERSON> thông báo về cửa sổ cha n<PERSON>u cần
      if (window.opener) {
        window.opener.postMessage({ status: "success" }, "*");
      }
      navigateTo("/profile/orders");
      if (route.query.redirect !== "true") {
        window.close();
      }
    }
  }, 1000);
});

// Fireworks effect state
const showFireworks = ref(false);

// Trigger fireworks on mount if payment is successful
onMounted(() => {
  if (paymentStatus === "success") {
    showFireworks.value = true;
    // Hide fireworks after 4 seconds to let them complete
    setTimeout(() => {
      showFireworks.value = false;
    }, 4000);
  }
});

// Manual redirect function
const goToOrders = () => {
  isRedirecting.value = true;
  navigateTo("/profile/orders");

  if (window.opener) {
    window.opener.postMessage({ status: "success" }, "*");
  }

  window.close();
};
</script>

<template>
  <UPage class="min-h-screen dark:bg-neutral-900/80">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-md mx-auto">
        <!-- Success Message -->
        <div v-if="paymentStatus === 'success'" class="text-center space-y-6">
          <!-- Success Icon -->
          <div class="flex justify-center">
            <div
              class="w-20 h-20 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center success-icon-container"
            >
              <UIcon
                name="i-lucide-check-circle"
                class="w-10 h-10 text-green-600 dark:text-green-400 success-icon"
              />
            </div>
          </div>

          <!-- Success Title -->
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ t("payment.success.title") }}
          </h1>

          <!-- Success Message -->
          <p class="text-gray-600 dark:text-gray-300">
            {{ t("payment.success.message") }}
          </p>

          <!-- Order ID -->
          <div
            v-if="orderId"
            class="bg-gray-50 dark:bg-neutral-800 rounded-lg p-4"
          >
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ t("payment.success.orderId") }}
            </p>
            <p class="font-mono text-sm text-gray-900 dark:text-white mt-1">
              {{ orderId }}
            </p>
          </div>

          <!-- Countdown and Redirect -->
          <div class="space-y-4">
            <p class="text-gray-600 dark:text-gray-300">
              {{ t("payment.success.redirecting", { seconds: countdown }) }}
            </p>

            <div class="flex justify-center space-x-4">
              <UButton
                :loading="isRedirecting"
                color="primary"
                @click="goToOrders"
              >
                {{ t("payment.success.viewOrders") }}
              </UButton>
            </div>
          </div>
        </div>

        <!-- Error/Other Status -->
        <div v-else class="text-center space-y-6">
          <div class="flex justify-center">
            <div
              class="w-20 h-20 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center"
            >
              <UIcon
                name="i-lucide-x-circle"
                class="w-10 h-10 text-red-600 dark:text-red-400"
              />
            </div>
          </div>

          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ t("payment.error.title") }}
          </h1>

          <p class="text-gray-600 dark:text-gray-300">
            {{ t("payment.error.message") }}
          </p>

          <UButton color="primary" @click="goToOrders">
            {{ t("payment.error.backToOrders") }}
          </UButton>
        </div>
      </div>
    </div>

    <!-- Fireworks Effect -->
    <div v-if="showFireworks" class="fireworks-container">
      <!-- Main fireworks -->
      <div class="firework firework-1" />
      <div class="firework firework-2" />
      <div class="firework firework-3" />
      <div class="firework firework-4" />
      <div class="firework firework-5" />

      <!-- Additional smaller fireworks -->
      <div class="firework-small firework-small-1" />
      <div class="firework-small firework-small-2" />
      <div class="firework-small firework-small-3" />
      <div class="firework-small firework-small-4" />

      <!-- Confetti particles -->
      <div class="confetti confetti-1" />
      <div class="confetti confetti-2" />
      <div class="confetti confetti-3" />
      <div class="confetti confetti-4" />
      <div class="confetti confetti-5" />
      <div class="confetti confetti-6" />
    </div>
  </UPage>
</template>

<style scoped>
.fireworks-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
  overflow: hidden;
}

.firework {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: firework 2s ease-out infinite;
}

.firework-small {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  animation: firework-small 1.8s ease-out infinite;
}

.confetti {
  position: absolute;
  width: 6px;
  height: 6px;
  animation: confetti-fall 3s linear infinite;
}

.firework-1 {
  top: 15%;
  left: 15%;
  background: linear-gradient(45deg, #ff6b6b, #ffd93d);
  animation-delay: 0s;
}

.firework-2 {
  top: 25%;
  left: 75%;
  background: linear-gradient(45deg, #74b9ff, #00b894);
  animation-delay: 0.4s;
}

.firework-3 {
  top: 65%;
  left: 25%;
  background: linear-gradient(45deg, #fd79a8, #fdcb6e);
  animation-delay: 0.8s;
}

.firework-4 {
  top: 45%;
  left: 85%;
  background: linear-gradient(45deg, #e17055, #a29bfe);
  animation-delay: 1.2s;
}

.firework-5 {
  top: 75%;
  left: 60%;
  background: linear-gradient(45deg, #00cec9, #fd79a8);
  animation-delay: 1.6s;
}

.firework-small-1 {
  top: 30%;
  left: 50%;
  background: linear-gradient(45deg, #ff7675, #fab1a0);
  animation-delay: 0.2s;
}

.firework-small-2 {
  top: 55%;
  left: 10%;
  background: linear-gradient(45deg, #00b894, #00cec9);
  animation-delay: 0.6s;
}

.firework-small-3 {
  top: 40%;
  left: 90%;
  background: linear-gradient(45deg, #a29bfe, #6c5ce7);
  animation-delay: 1s;
}

.firework-small-4 {
  top: 80%;
  left: 40%;
  background: linear-gradient(45deg, #fdcb6e, #e17055);
  animation-delay: 1.4s;
}

.confetti-1 {
  top: 0%;
  left: 20%;
  background: #ff6b6b;
  animation-delay: 0.1s;
}

.confetti-2 {
  top: 0%;
  left: 40%;
  background: #74b9ff;
  animation-delay: 0.3s;
}

.confetti-3 {
  top: 0%;
  left: 60%;
  background: #fd79a8;
  animation-delay: 0.5s;
}

.confetti-4 {
  top: 0%;
  left: 80%;
  background: #00b894;
  animation-delay: 0.7s;
}

.confetti-5 {
  top: 0%;
  left: 30%;
  background: #fdcb6e;
  animation-delay: 0.9s;
}

.confetti-6 {
  top: 0%;
  left: 70%;
  background: #a29bfe;
  animation-delay: 1.1s;
}

@keyframes firework {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 1;
    box-shadow:
      0 0 0 0 currentColor,
      0 0 0 0 currentColor,
      0 0 0 0 currentColor,
      0 0 0 0 currentColor;
  }
  15% {
    transform: scale(0.5) rotate(45deg);
    opacity: 1;
    box-shadow:
      15px 0 0 -2px currentColor,
      -15px 0 0 -2px currentColor,
      0 15px 0 -2px currentColor,
      0 -15px 0 -2px currentColor;
  }
  25% {
    transform: scale(1) rotate(90deg);
    opacity: 1;
    box-shadow:
      30px 0 0 -4px currentColor,
      -30px 0 0 -4px currentColor,
      0 30px 0 -4px currentColor,
      0 -30px 0 -4px currentColor;
  }
  50% {
    transform: scale(1.3) rotate(180deg);
    opacity: 0.9;
    box-shadow:
      50px 0 0 -6px currentColor,
      -50px 0 0 -6px currentColor,
      0 50px 0 -6px currentColor,
      0 -50px 0 -6px currentColor,
      35px 35px 0 -6px currentColor,
      -35px -35px 0 -6px currentColor,
      35px -35px 0 -6px currentColor,
      -35px 35px 0 -6px currentColor;
  }
  75% {
    transform: scale(1.5) rotate(270deg);
    opacity: 0.5;
    box-shadow:
      70px 0 0 -8px currentColor,
      -70px 0 0 -8px currentColor,
      0 70px 0 -8px currentColor,
      0 -70px 0 -8px currentColor,
      50px 50px 0 -8px currentColor,
      -50px -50px 0 -8px currentColor,
      50px -50px 0 -8px currentColor,
      -50px 50px 0 -8px currentColor;
  }
  100% {
    transform: scale(2) rotate(360deg);
    opacity: 0;
    box-shadow:
      90px 0 0 -10px transparent,
      -90px 0 0 -10px transparent,
      0 90px 0 -10px transparent,
      0 -90px 0 -10px transparent,
      65px 65px 0 -10px transparent,
      -65px -65px 0 -10px transparent,
      65px -65px 0 -10px transparent,
      -65px 65px 0 -10px transparent;
  }
}

@keyframes firework-small {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 1;
    box-shadow:
      0 0 0 0 currentColor,
      0 0 0 0 currentColor;
  }
  30% {
    transform: scale(1) rotate(90deg);
    opacity: 1;
    box-shadow:
      20px 0 0 -2px currentColor,
      -20px 0 0 -2px currentColor,
      0 20px 0 -2px currentColor,
      0 -20px 0 -2px currentColor;
  }
  60% {
    transform: scale(1.2) rotate(180deg);
    opacity: 0.7;
    box-shadow:
      30px 0 0 -3px currentColor,
      -30px 0 0 -3px currentColor,
      0 30px 0 -3px currentColor,
      0 -30px 0 -3px currentColor;
  }
  100% {
    transform: scale(1.5) rotate(270deg);
    opacity: 0;
    box-shadow:
      40px 0 0 -4px transparent,
      -40px 0 0 -4px transparent,
      0 40px 0 -4px transparent,
      0 -40px 0 -4px transparent;
  }
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

.success-icon-container {
  animation: success-pulse 2s ease-in-out infinite;
}

.success-icon {
  animation: success-glow 2s ease-in-out infinite;
}

@keyframes success-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 20px rgba(34, 197, 94, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

@keyframes success-glow {
  0% {
    filter: drop-shadow(0 0 5px rgba(34, 197, 94, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 15px rgba(34, 197, 94, 0.8));
  }
  100% {
    filter: drop-shadow(0 0 5px rgba(34, 197, 94, 0.5));
  }
}
</style>
